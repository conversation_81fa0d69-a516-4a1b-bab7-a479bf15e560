import 'dart:convert';

import 'package:quick_retail_mobile/core/data/models/category_response.dart';

ProductVariationResponse productVariationResponseFromJson(String str) =>
    ProductVariationResponse.fromJson(json.decode(str));

String productVariationResponseToJson(ProductVariationResponse data) =>
    json.encode(data.toJson());

class ProductVariationResponse {
  final bool? error;
  final String? message;
  final ProductVariationData? data;

  ProductVariationResponse({
    this.error,
    this.message,
    this.data,
  });

  factory ProductVariationResponse.fromJson(Map<String, dynamic> json) =>
      ProductVariationResponse(
        error: json["error"],
        message: json["message"],
        data: json["data"] == null
            ? null
            : ProductVariationData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "error": error,
        "message": message,
        "data": data?.toJson(),
      };
}

class ProductVariationData {
  final String? productId;
  final int? categoryId;
  final int? subCategoryId;
  final int? locationId;
  final dynamic vendorId;
  final String? productName;
  final String? productCode;
  final String? sku;
  final String? shortDescription;
  final String? longDescription;
  final dynamic featuredImage;
  final dynamic imagePath;
  final dynamic ean;
  final dynamic taxId;
  final String? costPrice;
  final String? sellingPrice;
  final dynamic discountPercentage;
  final String? promotionalPrice;
  final dynamic promotionalStartDate;
  final dynamic promotionalEndDate;
  final int? totalQuantity;
  final dynamic safetyInstructions;
  final dynamic complianceCertificates;
  final String? tags;
  final dynamic notes;
  final dynamic reason;
  final String? status;
  final int? hasVariations;
  final int? isDraft;
  final int? createdBy;
  final dynamic deletedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Location? location;
  final Category? category;
  final Category? subCategory;
  final List<ProductVariation>? productVariations;

  ProductVariationData({
    this.productId,
    this.categoryId,
    this.subCategoryId,
    this.locationId,
    this.vendorId,
    this.productName,
    this.productCode,
    this.sku,
    this.shortDescription,
    this.longDescription,
    this.featuredImage,
    this.imagePath,
    this.ean,
    this.taxId,
    this.costPrice,
    this.sellingPrice,
    this.discountPercentage,
    this.promotionalPrice,
    this.promotionalStartDate,
    this.promotionalEndDate,
    this.totalQuantity,
    this.safetyInstructions,
    this.complianceCertificates,
    this.tags,
    this.notes,
    this.reason,
    this.status,
    this.hasVariations,
    this.isDraft,
    this.createdBy,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
    this.location,
    this.category,
    this.subCategory,
    this.productVariations,
  });

  factory ProductVariationData.fromJson(Map<String, dynamic> json) =>
      ProductVariationData(
        productId: json["productID"],
        categoryId: json["category_id"],
        subCategoryId: json["sub_category_id"],
        locationId: json["location_id"],
        vendorId: json["vendor_id"],
        productName: json["product_name"],
        productCode: json["product_code"],
        sku: json["sku"],
        shortDescription: json["short_description"],
        longDescription: json["long_description"],
        featuredImage: json["featured_image"],
        imagePath: json["image_path"],
        ean: json["ean"],
        taxId: json["tax_id"],
        costPrice: json["cost_price"],
        sellingPrice: json["selling_price"],
        discountPercentage: json["discount_percentage"],
        promotionalPrice: json["promotional_price"],
        promotionalStartDate: json["promotional_start_date"],
        promotionalEndDate: json["promotional_end_date"],
        totalQuantity: json["total_quantity"],
        safetyInstructions: json["safety_instructions"],
        complianceCertificates: json["compliance_certificates"],
        tags: json["tags"],
        notes: json["notes"],
        reason: json["reason"],
        status: json["status"],
        hasVariations: json["has_variations"],
        isDraft: json["is_draft"],
        createdBy: json["created_by"],
        deletedAt: json["deleted_at"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        location: json["location"] == null
            ? null
            : Location.fromJson(json["location"]),
        category: json["category"] == null
            ? null
            : Category.fromJson(json["category"]),
        subCategory: json["sub_category"] == null
            ? null
            : Category.fromJson(json["sub_category"]),
        productVariations: json["product_variations"] == null
            ? []
            : List<ProductVariation>.from(json["product_variations"]!
                .map((x) => ProductVariation.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "productID": productId,
        "category_id": categoryId,
        "sub_category_id": subCategoryId,
        "location_id": locationId,
        "vendor_id": vendorId,
        "product_name": productName,
        "product_code": productCode,
        "sku": sku,
        "short_description": shortDescription,
        "long_description": longDescription,
        "featured_image": featuredImage,
        "image_path": imagePath,
        "ean": ean,
        "tax_id": taxId,
        "cost_price": costPrice,
        "selling_price": sellingPrice,
        "discount_percentage": discountPercentage,
        "promotional_price": promotionalPrice,
        "promotional_start_date": promotionalStartDate,
        "promotional_end_date": promotionalEndDate,
        "total_quantity": totalQuantity,
        "safety_instructions": safetyInstructions,
        "compliance_certificates": complianceCertificates,
        "tags": tags,
        "notes": notes,
        "reason": reason,
        "status": status,
        "has_variations": hasVariations,
        "is_draft": isDraft,
        "created_by": createdBy,
        "deleted_at": deletedAt,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "location": location?.toJson(),
        "category": category?.toJson(),
        "sub_category": subCategory?.toJson(),
        "product_variations": productVariations == null
            ? []
            : List<dynamic>.from(productVariations!.map((x) => x.toJson())),
      };
}

class Location {
  final int? id;
  final String? name;

  Location({
    this.id,
    this.name,
  });

  factory Location.fromJson(Map<String, dynamic> json) => Location(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}

class ProductVariation {
  final int? id;
  final String? variationId;
  final String? name;
  final String? sku;
  final String? ean;
  final String? code;
  final String? costPrice;
  final String? sellingPrice;
  final int? quantity;
  final String? reorderLevel;
  final String? imagePath;
  final dynamic reason;
  final int? quantitySupplied;
  final int? quantityAvailable;
  final int? quantitySold;
  final int? quantityAvailableInStores;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? stockStatus;
  final List<VariationAttribute>? variationAttributes;

  ProductVariation({
    this.id,
    this.variationId,
    this.name,
    this.sku,
    this.ean,
    this.code,
    this.costPrice,
    this.sellingPrice,
    this.quantity,
    this.reorderLevel,
    this.imagePath,
    this.reason,
    this.quantitySupplied,
    this.quantityAvailable,
    this.quantitySold,
    this.quantityAvailableInStores,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.stockStatus,
    this.variationAttributes,
  });

  factory ProductVariation.fromJson(Map<String, dynamic> json) =>
      ProductVariation(
        id: json["id"],
        variationId: json["variationID"],
        name: json["name"],
        sku: json["sku"],
        ean: json["ean"],
        code: json["code"],
        costPrice: json["cost_price"],
        sellingPrice: json["selling_price"],
        quantity: json["quantity"],
        reorderLevel: json["reorder_level"],
        imagePath: json["image_path"],
        reason: json["reason"],
        quantitySupplied: json["quantity_supplied"],
        quantityAvailable: json["quantity_available"],
        quantitySold: json["quantity_sold"],
        quantityAvailableInStores: json["quantity_available_in_stores"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        stockStatus: json["stock_status"],
        variationAttributes: json["variation_attributes"] == null
            ? []
            : List<VariationAttribute>.from(json["variation_attributes"]!
                .map((x) => VariationAttribute.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "variationID": variationId,
        "name": name,
        "sku": sku,
        "ean": ean,
        "code": code,
        "cost_price": costPrice,
        "selling_price": sellingPrice,
        "quantity": quantity,
        "reorder_level": reorderLevel,
        "image_path": imagePath,
        "reason": reason,
        "quantity_supplied": quantitySupplied,
        "quantity_available": quantityAvailable,
        "quantity_sold": quantitySold,
        "quantity_available_in_stores": quantityAvailableInStores,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "stock_status": stockStatus,
        "variation_attributes": variationAttributes == null
            ? []
            : List<dynamic>.from(variationAttributes!.map((x) => x.toJson())),
      };
}

class VariationAttribute {
  final int? id;
  final int? productVariationId;
  final String? optionType;
  final String? optionValue;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  VariationAttribute({
    this.id,
    this.productVariationId,
    this.optionType,
    this.optionValue,
    this.createdAt,
    this.updatedAt,
  });

  factory VariationAttribute.fromJson(Map<String, dynamic> json) =>
      VariationAttribute(
        id: json["id"],
        productVariationId: json["product_variation_id"],
        optionType: json["option_type"],
        optionValue: json["option_value"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "product_variation_id": productVariationId,
        "option_type": optionType,
        "option_value": optionValue,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
