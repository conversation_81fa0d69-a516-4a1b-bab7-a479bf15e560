import 'dart:io';

class CreateProductArg {
  final String? productId;
  final String? variationId;
  final String? productName;
  final String? sku;
  final String? ean;
  final int? categoryId;
  final int? subCategoryId;
  final String? shortDescription;
  final String? longDescription;
  final int? locationId;
  final bool? hasVariations;
  final String? tags;
  final List<VariationArg>? variations;
  final int? promotionalPrice;
  final String? promotionalStartDate;
  final String? promotionalEndDate;
  final String? safetyInstructions;
  final String? certificates;
  final List<String>? imagePath;
  final List<File>? imageFile;

  CreateProductArg({
    this.productId,
    this.variationId,
    this.productName,
    this.sku,
    this.ean,
    this.categoryId,
    this.subCategoryId,
    this.shortDescription,
    this.longDescription,
    this.locationId,
    this.hasVariations,
    this.tags,
    this.variations,
    this.promotionalPrice,
    this.promotionalStartDate,
    this.promotionalEndDate,
    this.safetyInstructions,
    this.certificates,
    this.imagePath,
    this.imageFile,
  });

  Map<String, dynamic> toJson() => {
        "variationID": variationId,
        "product_name": productName,
        "sku": sku,
        "ean": ean,
        "category_id": categoryId,
        "sub_category_id": subCategoryId,
        "short_description": shortDescription,
        "long_description": longDescription,
        "location_id": locationId,
        "has_variations": hasVariations,
        "tags": tags,
        "variations": variations == null
            ? []
            : List<dynamic>.from(variations!.map((x) => x.toJson())),
        "promotional_price": promotionalPrice,
        "promotional_start_date": promotionalStartDate,
        "promotional_end_date": promotionalEndDate,
        "safety_instructions": safetyInstructions,
        "certificates": certificates,
        "image_path": imagePath == null
            ? []
            : List<dynamic>.from(imagePath!.map((x) => x)),
      };
}

class VariationArg {
  final String? variationID;
  final double? costPrice;
  final double? sellingPrice;
  final int? quantity;
  final int? reorderLevel;
  final String? size;
  final String? colour;
  String? image;

  VariationArg({
    this.variationID,
    this.costPrice,
    this.sellingPrice,
    this.quantity,
    this.reorderLevel,
    this.size,
    this.colour,
    this.image,
  });

  Map<String, dynamic> toJson() => {
        "variationID": variationID,
        "cost_price": costPrice,
        "selling_price": sellingPrice,
        "quantity": quantity,
        "reorder_level": reorderLevel,
        "size": size,
        "colour": colour,
        "image": image,
      };
}
