import 'dart:async';
import 'dart:convert';

import 'package:quick_retail_mobile/core/constants/api_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/request_type.dart';
import 'package:quick_retail_mobile/core/data/models/product_response.dart';
import 'package:quick_retail_mobile/core/data/models/product_variation.dart';
import 'package:quick_retail_mobile/core/data/models/responses/default_response.dart';
import 'package:quick_retail_mobile/core/data/network_manager/network_manager.dart';

class ProductProvider {
  // Create Product
  Future<DefaultResponse> createProduct([Map<String, dynamic>? details]) async {
    var completer = Completer<DefaultResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.createProduct,
              useAuth: true, body: jsonEncode(details));
      var result = DefaultResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  Future<ProductResponse> fetchAllProducts(
      [Map<String, dynamic>? details]) async {
    var completer = Completer<ProductResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.allProducts,
              useAuth: true, body: jsonEncode(details));
      var result = ProductResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  // Single Product
  Future<ProductData> fetchSingleProduct(String productId) async {
    var completer = Completer<ProductData>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(
              RequestType.get, ApiRoutes.getSingleProduct(productId),
              useAuth: true);
      var result = ProductData.fromJson(response['data']);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  // Delete Product
  Future<DefaultResponse> deleteProduct(String productId) async {
    var completer = Completer<DefaultResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(
              RequestType.delete, ApiRoutes.deleteProduct(productId),
              useAuth: true);
      var result = DefaultResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  // Update Product
  Future<DefaultResponse> updateProduct(String productId,
      [Map<String, dynamic>? details]) async {
    var completer = Completer<DefaultResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(
              RequestType.put, ApiRoutes.updateProduct(productId),
              useAuth: true, body: jsonEncode(details));
      var result = DefaultResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  // getProductDetailsVariationtakes product Id to get its variations
  Future<ProductVariationResponse> getProductDetailsVariation(
      String productId) async {
    var completer = Completer<ProductVariationResponse>();
    try {
      Map<String, dynamic> response =
          await NetworkManager().networkRequestManager(
        RequestType.get,
        ApiRoutes.editProduct(productId),
        useAuth: true,
      );
      var result = ProductVariationResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }
}
