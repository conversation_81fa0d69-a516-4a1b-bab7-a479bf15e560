import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';

/// Common validation functions for ChipField
class ChipFieldValidators {
  /// Email validation
  static String? email(String text) {
    final emailRegex =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(text)) {
      return 'Invalid email format';
    }
    return null;
  }

  /// Phone number validation (11+ digits)
  static String? phoneNumber(String text) {
    final phoneRegex = RegExp(r'^\d{11,}$');
    if (text.length < 11) {
      return 'Phone number must be at least 11 digits';
    }
    if (!phoneRegex.hasMatch(text)) {
      return 'Phone number must contain only digits';
    }
    return null;
  }

  /// Minimum length validation
  static String? Function(String) minLength(int min) {
    return (String text) {
      if (text.length < min) {
        return 'Must be at least $min characters';
      }
      return null;
    };
  }

  /// Maximum length validation
  static String? Function(String) maxLength(int max) {
    return (String text) {
      if (text.length > max) {
        return 'Must be at most $max characters';
      }
      return null;
    };
  }

  /// No spaces validation
  static String? noSpaces(String text) {
    if (text.contains(' ')) {
      return 'Spaces are not allowed';
    }
    return null;
  }

  /// Alphanumeric only validation
  static String? alphanumeric(String text) {
    final alphanumericRegex = RegExp(r'^[a-zA-Z0-9]+$');
    if (!alphanumericRegex.hasMatch(text)) {
      return 'Only letters and numbers are allowed';
    }
    return null;
  }
}

/// A reusable widget for collecting tags/chips with customizable validation
///
/// This widget allows users to input multiple text items as chips/tags.
/// It supports:
/// - Initial values
/// - Custom validation for individual items
/// - Maximum and minimum item limits
/// - Customizable appearance
/// - Form validation integration
///
/// Example usage:
/// ```dart
/// ChipField(
///   labelText: 'Tags',
///   hintText: 'Enter tags',
///   initialValues: ['tag1', 'tag2'],
///   onChanged: (tags) => print('Tags: $tags'),
///   max: 10,
///   itemValidator: (text) => text.length < 2 ? 'Too short' : null,
/// )
/// ```
class ChipField extends StatefulWidget {
  // Get the current state of the widget
  ChipFieldState? of(BuildContext context) =>
      context.findAncestorStateOfType<ChipFieldState>();

  /// Optional controller for the text field
  final TextEditingController? controller;

  /// Label text displayed above the field
  final String? labelText;

  /// Hint text displayed inside the field when empty
  final String? hintText;

  /// Optional text to display next to the label (e.g. "(Optional)")
  final String? optionalText;

  /// Background color for the chips
  final Color? chipColor;

  /// Text color for the chips
  final Color? chipTextColor;

  /// Background color for the field
  final Color? fillColor;

  /// Border color for the field
  final Color? borderColor;

  /// Border radius for the field
  final double? borderRadius;

  /// Callback when the list of chips/tags changes
  final Function(List<String>)? onChanged;

  /// Validator function for the field
  final String? Function(String?)? validator;

  /// Whether to show the label header
  final bool showLabelHeader;

  /// Whether to hide the border
  final bool hideBorder;

  /// Initial list of chips/tags
  final List<String>? initialValues;

  /// Whether at least one chip/tag is required
  final bool isRequired;

  /// Maximum number of chips/tags allowed (null means no maximum)
  final int? max;

  /// Minimum number of chips/tags required (null means no minimum)
  /// Note: If isRequired is true, min is at least 1
  final int? min;

  /// Custom validation function for individual chips/tags
  /// If null, no validation is performed on individual items
  final String? Function(String)? itemValidator;

  const ChipField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.optionalText,
    this.chipColor,
    this.chipTextColor,
    this.fillColor,
    this.borderColor,
    this.borderRadius,
    this.onChanged,
    this.validator,
    this.showLabelHeader = true,
    this.hideBorder = false,
    this.initialValues,
    this.isRequired = false,
    this.max,
    this.min,
    this.itemValidator,
  });

  @override
  State<ChipField> createState() => ChipFieldState();
}

class ChipFieldState extends State<ChipField> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final List<String> _chips = [];
  String? _errorText;

  /// Method to validate the field - can be called from a parent Form
  bool validate() {
    // Check minimum number of chips/tags
    final effectiveMin = widget.min ?? (widget.isRequired ? 1 : 0);
    if (_chips.length < effectiveMin) {
      setState(() {
        if (effectiveMin == 1) {
          _errorText = 'At least one item is required';
        } else {
          _errorText = 'At least $effectiveMin items are required';
        }
      });
      return false;
    }

    // Check maximum number of chips/tags
    if (widget.max != null && _chips.length > widget.max!) {
      setState(() {
        _errorText = 'Maximum ${widget.max} items allowed';
      });
      return false;
    }

    setState(() {
      _errorText = null;
    });
    return true;
  }

  TextEditingController get _effectiveController =>
      widget.controller ?? _controller;

  @override
  void initState() {
    super.initState();
    if (widget.initialValues != null) {
      _chips.addAll(widget.initialValues!);
    }
    _effectiveController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _effectiveController.removeListener(_onTextChanged);
    }
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _effectiveController.text;

    // Process text when space or comma is entered
    if (text.endsWith(' ') || text.endsWith(',')) {
      final trimmedText = text.replaceAll(',', '').trim();
      if (trimmedText.isNotEmpty) {
        _processText(trimmedText);
      }
      return;
    }

    // Real-time validation for better user feedback
    if (text.isNotEmpty && widget.itemValidator != null) {
      final error = widget.itemValidator!(text);
      setState(() {
        _errorText = error;
      });
    } else {
      setState(() {
        _errorText = null;
      });
    }
  }

  void _processText(String text) {
    // Use custom validator if provided, otherwise allow all text
    final validationError = widget.itemValidator?.call(text);

    // Check if we've reached the maximum number of chips/tags
    if (validationError == null) {
      if (widget.max != null && _chips.length >= widget.max!) {
        setState(() {
          _errorText = 'Maximum ${widget.max} items allowed';
          _effectiveController.clear();
        });
        return;
      }

      setState(() {
        if (!_chips.contains(text)) {
          _chips.add(text);
          widget.onChanged?.call(_chips);
        }
        _effectiveController.clear();
        _errorText = null;
      });
    } else {
      setState(() {
        _errorText = validationError;
      });
    }
  }

  void _removeChip(String chip) {
    setState(() {
      _chips.remove(chip);
      widget.onChanged?.call(_chips);
      // Clear error if we were at max capacity
      if (widget.max != null && _errorText?.contains('Maximum') == true) {
        _errorText = null;
      }
    });
  }

  /// Check if the field is at its maximum capacity
  bool isAtMaxCapacity() {
    return widget.max != null && _chips.length >= widget.max!;
  }

  /// Get the current count of chips/tags
  int getChipCount() {
    return _chips.length;
  }

  /// Get the current list of chips/tags
  List<String> getChips() {
    return List.from(_chips);
  }

  /// Add a chip/tag programmatically
  void addChip(String chip) {
    if (!_chips.contains(chip) &&
        (widget.max == null || _chips.length < widget.max!)) {
      setState(() {
        _chips.add(chip);
        widget.onChanged?.call(_chips);
      });
    }
  }

  /// Remove a chip/tag programmatically
  void removeChip(String chip) {
    if (_chips.contains(chip)) {
      setState(() {
        _chips.remove(chip);
        widget.onChanged?.call(_chips);
      });
    }
  }

  /// Clear all chips/tags
  void clearChips() {
    setState(() {
      _chips.clear();
      widget.onChanged?.call(_chips);
      _errorText = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showLabelHeader)
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        widget.labelText ?? 'Enter tags',
                        style: TextStyle(
                          color: theme.textTheme.bodyMedium?.color ??
                              Colors.grey[600],
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      if (widget.optionalText != null)
                        Text(
                          widget.optionalText!,
                          style: TextStyle(
                            color: theme.textTheme.bodySmall?.color ??
                                Colors.grey[500],
                            fontSize: 12.sp,
                          ),
                        ),
                    ],
                  ),
                  // Show counter when max is set
                  if (widget.max != null)
                    Text(
                      '${_chips.length}/${widget.max}',
                      style: TextStyle(
                        color: isAtMaxCapacity()
                            ? colorScheme.error
                            : theme.textTheme.bodySmall?.color ??
                                Colors.grey[500],
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                ],
              ),
              SizedBox(height: 4.h)
            ],
          ),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: widget.fillColor ?? colorScheme.surface,
            borderRadius: BorderRadius.circular(widget.borderRadius ?? 8.r),
            border: widget.hideBorder
                ? null
                : Border.all(
                    color: widget.borderColor ?? ColorPath.mischkaGrey,
                    width: 1,
                  ),
          ),
          padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 8.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 8.w,
                runSpacing: 8.h,
                children: _chips.map((chip) => _buildChip(chip)).toList(),
              ),
              TextField(
                controller: _effectiveController,
                focusNode: _focusNode,
                enabled: !isAtMaxCapacity(),
                style: TextStyle(
                  color: isAtMaxCapacity()
                      ? theme.disabledColor
                      : theme.textTheme.bodyLarge?.color ?? Colors.black,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                ),
                decoration: InputDecoration(
                  hintText: isAtMaxCapacity()
                      ? 'Maximum items reached'
                      : (widget.hintText ?? 'Enter tags'),
                  hintStyle: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color: isAtMaxCapacity()
                        ? theme.disabledColor
                        : theme.hintColor,
                  ),
                  border: InputBorder.none,
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(vertical: 8.h),
                ),
                onSubmitted: (value) {
                  if (!isAtMaxCapacity() && value.trim().isNotEmpty) {
                    _processText(value.trim());
                  }
                },
              ),
            ],
          ),
        ),
        if (_errorText != null)
          Padding(
            padding: EdgeInsets.only(top: 4.h, left: 4.w),
            child: Text(
              _errorText!,
              style: TextStyle(
                color: colorScheme.error,
                fontSize: 12.sp,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildChip(String chip) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: widget.chipColor ?? ColorPath.grey100,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            chip,
            style: TextStyle(
              fontSize: 12.sp,
              color: widget.chipTextColor ?? ColorPath.oxfordBlue,
            ),
          ),
          SizedBox(width: 4.w),
          InkWell(
            onTap: () => _removeChip(chip),
            child: Icon(
              Icons.close,
              size: 14.sp,
              color: ColorPath.oxfordBlue,
            ),
          ),
        ],
      ),
    );
  }
}
