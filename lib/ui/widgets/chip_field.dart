import 'package:flutter/material.dart';

/// A reusable widget for collecting email addresses and phone numbers as chips
/// Similar to CustomTextField but specialized for invitations
class ChipField extends StatefulWidget {
  // Get the current state of the widget
  ChipFieldState? of(BuildContext context) =>
      context.findAncestorStateOfType<ChipFieldState>();

  /// Optional controller for the text field
  final TextEditingController? controller;

  /// Label text displayed above the field
  final String? labelText;

  /// Hint text displayed inside the field when empty
  final String? hintText;

  /// Optional text to display next to the label (e.g. "(Optional)")
  final String? optionalText;

  /// Background color for the chips
  final Color? chipColor;

  /// Text color for the chips
  final Color? chipTextColor;

  /// Background color for the field
  final Color? fillColor;

  /// Border color for the field
  final Color? borderColor;

  /// Border radius for the field
  final double? borderRadius;

  /// Callback when the list of invitees changes
  final Function(List<String>)? onInviteesChanged;

  /// Validator function for the field
  final String? Function(String?)? validator;

  /// Whether to show the label header
  final bool showLabelHeader;

  /// Whether to hide the border
  final bool hideBorder;

  /// Initial list of invitees
  final List<String>? initialInvitees;

  /// Whether at least one invitee is required
  final bool isRequired;

  /// Maximum number of invitees allowed (null means no maximum)
  final int? max;

  /// Minimum number of invitees required (null means no minimum)
  /// Note: If isRequired is true, min is at least 1
  final int? min;

  const ChipField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.optionalText,
    this.chipColor,
    this.chipTextColor,
    this.fillColor,
    this.borderColor,
    this.borderRadius,
    this.onInviteesChanged,
    this.validator,
    this.showLabelHeader = true,
    this.hideBorder = false,
    this.initialInvitees,
    this.isRequired = false,
    this.max,
    this.min,
  });

  @override
  State<ChipField> createState() => ChipFieldState();
}

class ChipFieldState extends State<ChipField> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<String> _invitees = [];
  String? _errorText;

  /// Method to validate the field - can be called from a parent Form
  bool validate() {
    // Check minimum number of invitees
    final effectiveMin = widget.min ?? (widget.isRequired ? 1 : 0);
    if (_invitees.length < effectiveMin) {
      setState(() {
        if (effectiveMin == 1) {
          _errorText = 'At least one invitee is required';
        } else {
          _errorText = 'At least $effectiveMin invitees are required';
        }
      });
      return false;
    }

    // Check maximum number of invitees
    if (widget.max != null && _invitees.length > widget.max!) {
      setState(() {
        _errorText = 'Maximum ${widget.max} invitees allowed';
      });
      return false;
    }

    setState(() {
      _errorText = null;
    });
    return true;
  }

  // Patterns to validate emails and phone numbers
  final RegExp _emailPattern =
      RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
  final RegExp _phonePattern =
      RegExp(r'^\d{11,}$'); // At least 11 digits for phone numbers

  TextEditingController get _effectiveController =>
      widget.controller ?? _controller;

  @override
  void initState() {
    super.initState();
    if (widget.initialInvitees != null) {
      _invitees = List.from(widget.initialInvitees!);
    }
    _effectiveController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _effectiveController.removeListener(_onTextChanged);
    }
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _effectiveController.text;

    // Process text when space or comma is entered
    if (text.endsWith(' ') || text.endsWith(',')) {
      final trimmedText = text.replaceAll(',', '').trim();
      if (trimmedText.isNotEmpty) {
        _processText(trimmedText);
      }
      return;
    }

    // Real-time validation for better user feedback
    if (text.isNotEmpty) {
      final error = _getValidationError(text);
      setState(() {
        _errorText = error;
      });
    } else {
      setState(() {
        _errorText = null;
      });
    }
  }

  String? _getValidationError(String text) {
    if (text.isEmpty) {
      return null;
    }

    // Check if it looks like an email (contains @)
    if (text.contains('@')) {
      if (!_emailPattern.hasMatch(text)) {
        return 'Invalid email format';
      }
    }
    // Otherwise treat as phone number
    else {
      if (text.length < 11) {
        return 'Phone number must be at least 11 digits';
      }
      if (!_phonePattern.hasMatch(text)) {
        return 'Phone number must contain only digits';
      }
    }

    return null;
  }

  void _processText(String text) {
    final validationError = _getValidationError(text);

    // Check if we've reached the maximum number of invitees
    if (validationError == null) {
      if (widget.max != null && _invitees.length >= widget.max!) {
        setState(() {
          _errorText = 'Maximum ${widget.max} invitees allowed';
          _effectiveController.clear();
        });
        return;
      }

      setState(() {
        if (!_invitees.contains(text)) {
          _invitees.add(text);
          widget.onInviteesChanged?.call(_invitees);
        }
        _effectiveController.clear();
        _errorText = null;
      });
    } else {
      setState(() {
        _errorText = validationError;
      });
    }
  }

  void _removeInvitee(String invitee) {
    setState(() {
      _invitees.remove(invitee);
      widget.onInviteesChanged?.call(_invitees);
      // Clear error if we were at max capacity
      if (widget.max != null && _errorText?.contains('Maximum') == true) {
        _errorText = null;
      }
    });
  }

  /// Check if the field is at its maximum capacity
  bool isAtMaxCapacity() {
    return widget.max != null && _invitees.length >= widget.max!;
  }

  /// Get the current count of invitees
  int getInviteeCount() {
    return _invitees.length;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showLabelHeader)
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        widget.labelText ?? 'Invite by phone number or email',
                        style: TextStyle(
                          color: AppColors.neutral400,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      Text(
                        widget.optionalText ?? '',
                        style: TextStyle(
                          color: AppColors.neutral400,
                          fontSize: 12.sp,
                        ),
                      ),
                    ],
                  ),
                  // Show counter when max is set
                  if (widget.max != null)
                    Text(
                      '${_invitees.length}/${widget.max}',
                      style: TextStyle(
                        color: isAtMaxCapacity()
                            ? AppColors.red.withAlpha(204)
                            : AppColors.neutral400,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                ],
              ),
              const YBox(4)
            ],
          ),
        Container(
          width: Sizer.screenWidth,
          decoration: BoxDecoration(
            color: widget.fillColor ?? AppColors.white,
            borderRadius: BorderRadius.circular(widget.borderRadius ?? 8.r),
            border: widget.hideBorder
                ? null
                : Border.all(
                    color: widget.borderColor ?? AppColors.secondary300,
                    width: 1,
                  ),
          ),
          padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 8.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 8.w,
                runSpacing: 8.h,
                children:
                    _invitees.map((invitee) => _buildChip(invitee)).toList(),
              ),
              TextField(
                controller: _effectiveController,
                focusNode: _focusNode,
                enabled: !isAtMaxCapacity(),
                style: TextStyle(
                  color: isAtMaxCapacity()
                      ? AppColors.neutral200
                      : AppColors.black,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                ),
                decoration: InputDecoration(
                  hintText: isAtMaxCapacity()
                      ? 'Maximum invitees reached'
                      : (widget.hintText ?? 'Enter email or phone number'),
                  hintStyle: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color: isAtMaxCapacity()
                        ? AppColors.neutral200
                        : AppColors.neutral100.withAlpha(51), // ~0.2 opacity
                  ),
                  border: InputBorder.none,
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(vertical: 8.h),
                ),
                onSubmitted: (value) {
                  if (!isAtMaxCapacity() && value.trim().isNotEmpty) {
                    _processText(value.trim());
                  }
                },
              ),
            ],
          ),
        ),
        if (_errorText != null)
          Padding(
            padding: EdgeInsets.only(top: 4.h, left: 4.w),
            child: Text(
              _errorText!,
              style: TextStyle(
                color: AppColors.red.withAlpha(204), // ~0.8 opacity
                fontSize: 12.sp,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildChip(String invitee) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: widget.chipColor ??
            AppColors.gray87.withValues(alpha: 0.1), // ~0.2 opacity
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            invitee,
            style: TextStyle(
              fontSize: 12.sp,
              color: widget.chipTextColor ?? AppColors.neutral300,
            ),
          ),
          SizedBox(width: 4.w),
          InkWell(
            onTap: () => _removeInvitee(invitee),
            child: Container(
              padding: EdgeInsets.all(2.r),
              decoration: const BoxDecoration(
                color: AppColors.gray62,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                size: 14.sp,
                color: AppColors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
