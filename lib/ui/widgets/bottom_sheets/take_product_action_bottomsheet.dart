import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/create_product_arg.dart';
import 'package:quick_retail_mobile/core/data/models/quick_action_item.dart';
import 'package:quick_retail_mobile/core/data/services/navigation_service.dart';
import 'package:quick_retail_mobile/core/data/view_models/product_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/custom_bottom_sheet.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/show_flush_bar.dart';

class TakeProductActionBottomsheet extends ConsumerStatefulWidget {
  const TakeProductActionBottomsheet({
    super.key,
    this.isVariation = false,
    required this.viewProductArg,
  });

  final bool isVariation;
  final CreateProductArg viewProductArg;

  @override
  ConsumerState<TakeProductActionBottomsheet> createState() =>
      _TakeProductActionBottomsheetState();
}

class _TakeProductActionBottomsheetState
    extends ConsumerState<TakeProductActionBottomsheet> {
  late final List<QuickActionsItem> quickActions;
  bool _isDeleting = false;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    quickActions = [
      QuickActionsItem(
        actionName: "Edit Product",
        subtitle: "Save and update new changes ",
        assetName: "edit",
        onPressed: () {
          bottomSheetWrapper(
              context:
                  locator<NavigationService>().navigationKey.currentContext!,
              isDismissible: !_isUpdating, // Prevent dismissal during update
              enableDrag: !_isUpdating, // Prevent drag dismissal during update
              child: Consumer(
                builder: (context, ref, child) {
                  final productVm = ref.watch(productViewModel);
                  final isLoading =
                      _isUpdating || productVm.updateState == ViewState.busy;

                  return CustomBottomSheet(
                    title: "Save and Update New Changes ?",
                    subTitle:
                        "Are you sure you want to Save and update new changes on this ${widget.isVariation ? 'Variation' : 'Simple'} product ? Kindly note that this new changes would override the old changes. ",
                    firstButtonText: "Yes, Save and Update New Changes",
                    secondButtonText: "No, Close",
                    showLoader: isLoading,
                    onPressedFirst: isLoading
                        ? null
                        : () async {
                            await _handleUpdateProduct();
                          },
                    onPressedSecond: isLoading
                        ? null
                        : () {
                            _pop();
                          },
                  );
                },
              ));
        },
      ),
      // QuickActionsItem(
      //   actionName: "Deactivate Product",
      //   subtitle: "Temporarily deactivate product for sale",
      //   assetName: "toggle",
      //   onPressed: () {
      //     bottomSheetWrapper(
      //         context:
      //             locator<NavigationService>().navigationKey.currentContext!,
      //         child: CustomBottomSheet(
      //           title: "Save and Update New Changes ?",
      //           subTitle:
      //               "Are you sure you want to Save and update new changes on this Simple product ? Kindly note that this new changes would override the old changes. ",
      //           firstButtonText: "Yes, Save and Update New Changes",
      //           secondButtonText: "No, Close",
      //           onPressedFirst: () {},
      //           onPressedSecond: () {
      //             popNavigation(
      //               context: locator<NavigationService>()
      //                   .navigationKey
      //                   .currentContext!,
      //             );
      //           },
      //         ));
      //   },
      // ),
      // QuickActionsItem(
      //   actionName: "Reactivate Product",
      //   subtitle: "Reactivate an already deactivated product",
      //   assetName: "toggle",
      //   onPressed: () {
      //     bottomSheetWrapper(
      //         context:
      //             locator<NavigationService>().navigationKey.currentContext!,
      //         child: CustomBottomSheet(
      //           title: "Save and Update New Changes ?",
      //           subTitle:
      //               "Are you sure you want to Save and update new changes on this Simple product ? Kindly note that this new changes would override the old changes. ",
      //           firstButtonText: "Yes, Save and Update New Changes",
      //           secondButtonText: "No, Close",
      //           onPressedFirst: () {},
      //           onPressedSecond: () {
      //             popNavigation(
      //               context: locator<NavigationService>()
      //                   .navigationKey
      //                   .currentContext!,
      //             );
      //           },
      //         ));
      //   },
      // ),
      QuickActionsItem(
        actionName: "Delete Product",
        subtitle: "Remove product and it details permanently",
        assetName: "trashFlamingo",
        onPressed: () {
          bottomSheetWrapper(
              context:
                  locator<NavigationService>().navigationKey.currentContext!,
              isDismissible: !_isDeleting, // Prevent dismissal during delete
              enableDrag: !_isDeleting, // Prevent drag dismissal during delete
              child: Consumer(
                builder: (context, ref, child) {
                  final productVm = ref.watch(productViewModel);
                  final isLoading =
                      _isDeleting || productVm.deleteState == ViewState.busy;

                  return CustomBottomSheet(
                    title:
                        "Delete ${widget.isVariation ? 'Variation' : 'Simple'} Product ?",
                    subTitle:
                        "Are you sure you want to delete this ${widget.isVariation ? 'Variation' : 'Simple'} product? Kindly note that this action is irreversible and permanent, therefore product data would be wiped out from the store database. ",
                    firstButtonText:
                        "Yes, Delete ${widget.isVariation ? 'Variation' : 'Simple'} product",
                    secondButtonText: "No, Close",
                    showLoader: isLoading,
                    onPressedFirst: isLoading
                        ? null
                        : () async {
                            await _handleDeleteProduct();
                          },
                    onPressedSecond: isLoading
                        ? null
                        : () {
                            _pop();
                          },
                  );
                },
              ));
        },
      ),
    ];
  }

  _fetchProducts() {
    ref.read(productViewModel).fetchAllProducts();
  }

  _pop() {
    popNavigation(
      context: locator<NavigationService>().navigationKey.currentContext!,
    );
  }

  Future<void> _handleUpdateProduct() async {
    if (_isUpdating) return; // Prevent multiple update operations

    setState(() {
      _isUpdating = true;
    });

    try {
      await ref.read(productViewModel.notifier).updateProduct(
            widget.viewProductArg.productId ?? "",
            widget.viewProductArg.toJson(),
          );

      if (!mounted) return;

      final productVm = ref.read(productViewModel);

      if (productVm.updateState == ViewState.retrieved) {
        // Success - close current modal first, then show success modal
        _pop();

        // Small delay to ensure modal is closed before showing success
        await Future.delayed(const Duration(milliseconds: 100));

        if (!mounted) return;

        bottomSheetWrapper(
            context: locator<NavigationService>().navigationKey.currentContext!,
            child: CustomBottomSheet(
              title:
                  '${widget.isVariation ? 'Variation' : 'Simple'} Product Updated',
              subTitle:
                  'Congratulations, you have successfully updated this ${widget.isVariation ? 'Variation' : 'Simple'} product',
              firstButtonText: "Manage all Product",
              secondButtonText: "Create New Product",
              onPressedFirst: () {
                _pop();
                _pop();
                _pop();
                _fetchProducts();
              },
              onPressedSecond: () {
                popNavigation(context: context);
              },
            ));
      } else {
        // Error - keep modal open and show error message
        if (mounted) {
          showFlushBar(
              context: context,
              message: productVm.updateMessage.isNotEmpty
                  ? productVm.updateMessage
                  : 'Failed to update product. Please try again.',
              success: false);
        }
      }
    } catch (error) {
      // Handle unexpected errors
      if (mounted) {
        showFlushBar(
            context: context,
            message: 'An unexpected error occurred. Please try again.',
            success: false);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _handleDeleteProduct() async {
    if (_isDeleting) return; // Prevent multiple delete operations

    setState(() {
      _isDeleting = true;
    });

    try {
      await ref
          .read(productViewModel.notifier)
          .deleteProduct(widget.viewProductArg.variationId ?? "");

      if (!mounted) return;

      final productVm = ref.read(productViewModel);

      if (productVm.deleteState == ViewState.retrieved) {
        // Success - close current modal first, then show success modal
        _pop();

        // Small delay to ensure modal is closed before showing success
        await Future.delayed(const Duration(milliseconds: 100));

        if (!mounted) return;

        bottomSheetWrapper(
            context: locator<NavigationService>().navigationKey.currentContext!,
            child: CustomBottomSheet(
              title:
                  '${widget.isVariation ? 'Variation' : 'Simple'} Product Deleted',
              subTitle:
                  'Congratulations, you have successfully deleted this ${widget.isVariation ? 'Variation' : 'Simple'} product',
              firstButtonText: "Manage all Product",
              secondButtonText: "Create New Product",
              onPressedFirst: () {
                _pop();
                _pop();
                _pop();
                _fetchProducts();
              },
              onPressedSecond: () {
                popNavigation(context: context);
              },
            ));
      } else {
        // Error - keep modal open and show error message
        if (mounted) {
          showFlushBar(
              context: context,
              message: productVm.deleteMessage.isNotEmpty
                  ? productVm.deleteMessage
                  : 'Failed to delete product. Please try again.',
              success: false);
        }
      }
    } catch (error) {
      // Handle unexpected errors
      if (mounted) {
        showFlushBar(
            context: context,
            message: 'An unexpected error occurred. Please try again.',
            success: false);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDeleting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const BottomSheetHeader(
          title: "Take action",
          subTitle: "Take action on a specific product today",
        ),
        SizedBox(
          height: 24.h,
        ),
        ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return Clickable(
                onPressed: quickActions[index].onPressed,
                child: Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                      border: Border.all(color: ColorPath.athensGrey),
                      borderRadius: BorderRadius.circular(8.r)),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: ColorPath.flamingo.withOpacity(.2)),
                        child: SvgPicture.asset(
                            Utilities.getSvg(quickActions[index].assetName)),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(quickActions[index].actionName),
                            Text(
                              quickActions[index].subtitle ?? '',
                              style: textTheme.bodySmall
                                  ?.copyWith(color: colorScheme.subTextPrimary),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 16.h,
              );
            },
            itemCount: quickActions.length),
        SizedBox(
          height: 24.h,
        ),
        CustomButton(
          onPressed: () {
            popNavigation(context: context);
          },
          borderColor: ColorPath.flamingo,
          bgColor: Colors.white,
          buttonTextColor: ColorPath.flamingo,
          buttonText: "No, Close",
        )
      ],
    );
  }
}
