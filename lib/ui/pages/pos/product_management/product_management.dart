import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/category_response.dart';
import 'package:quick_retail_mobile/core/data/models/product_response.dart';
import 'package:quick_retail_mobile/core/data/view_models/product_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/product_management/product_basic_information.dart';
import 'package:quick_retail_mobile/ui/pages/pos/product_management/view_product_variation_info.dart';
import 'package:quick_retail_mobile/ui/pages/pos/scan_product/scan_product.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/create_product_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/store_filter_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_icon.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/error_state.dart';
import 'package:quick_retail_mobile/ui/widgets/loadable_content_builder.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard_header.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_item.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProductMnagement extends ConsumerStatefulWidget {
  const ProductMnagement({super.key});

  @override
  ConsumerState<ProductMnagement> createState() => _ProductMnagementState();
}

class _ProductMnagementState extends ConsumerState<ProductMnagement> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchProducts();
    });
  }

  _fetchProducts() {
    ref.read(productViewModel).fetchAllProducts();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    // final colorScheme = Theme.of(context).colorScheme;
    final prodVm = ref.watch(productViewModel);
    return Scaffold(
      appBar: customAppBar(
          context: context,
          // preferredHeight: 16,
          title: "Product Management",
          centerTitle: true,
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 16.w),
              child: Row(
                children: [
                  CustomIcon(
                    imageAsset: Utilities.getSvg("addWhite"),
                    bgColor: ColorPath.flamingo,
                    onPressed: () {
                      bottomSheetWrapper(
                        context: context,
                        child: CreateProductBottomsheet(),
                      );
                    },
                  ),
                  SizedBox(
                    width: 8.w,
                  ),
                ],
              ),
            )
          ]),
      body: SingleChildScrollView(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h, bottom: 48.h),
        child: 1 + 1 == 3
            ? Padding(
                padding: const EdgeInsets.only(top: 150.0),
                child: EmptyState(
                  imageAsset: Utilities.getSvg('noProduct'),
                  title: "No Product",
                  subTitle:
                      "No currently do not have any product added. Add new product today to Start Making Sales",
                  buttonText: "Add New Product",
                ),
              )
            : Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "Products",
                            style: textTheme.titleMedium,
                          ),
                          SizedBox(
                            width: 4.w,
                          ),
                          ColorTag(
                            color: ColorPath.flamingo.withOpacity(.2),
                            child: Text(
                              prodVm.products.length.toString(),
                              style: textTheme.bodySmall
                                  ?.copyWith(color: ColorPath.flamingo),
                            ),
                          )
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: CustomIcon(
                          imageAsset: Utilities.getSvg("filter"),
                          bgColor: ColorPath.flamingo,
                          onPressed: () {
                            bottomSheetWrapper(
                                context: context,
                                child: const StoreFilterBottomsheet());
                          },
                        ),
                      ),
                    ],
                  ),
                  // SizedBox(
                  //   height: 15.h,
                  // ),
                  CustomTextField(
                    isCompulsory: false,
                    hintText: 'Search by Name, Product.....',
                    prefixIcon: Padding(
                      padding: const EdgeInsets.only(left: 8.0, right: 8.0),
                      child: SvgPicture.asset(Utilities.getSvg('search')),
                    ),
                    suffixIcon: Clickable(
                      onPressed: () {
                        pushNavigation(
                            context: context,
                            widget: const ScanProduct(),
                            routeName: NamedRoutes.scanProduct);
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(left: 8.0, right: 12.0),
                        child: SvgPicture.asset(Utilities.getSvg('scan')),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 46.h,
                  ),
                  DashBoardHeader(
                    title: "Overview",
                    subtitle: "An overview of product",
                    filterText: '3 Days Ago',
                    onPressed: () {},
                    padding: EdgeInsets.zero,
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  Skeletonizer(
                    enabled: prodVm.fetchAllState == ViewState.busy,
                    child: ProductManagementOverviewCard(
                      revenue: double.tryParse(
                              prodVm.productResponse?.data?.totalRevenue ??
                                  '0') ??
                          0,
                      totalActive: prodVm.productResponse?.data?.active ?? 0,
                      totalInactive:
                          prodVm.productResponse?.data?.inactive ?? 0,
                      totalProduct:
                          prodVm.productResponse?.data?.totalItems ?? 0,
                    ),
                  ),
                  LoadableContentBuilder(
                    isBusy: ref.watch(productViewModel).fetchAllState ==
                        ViewState.busy,
                    isError: ref.watch(productViewModel).fetchAllState ==
                        ViewState.error,
                    items: ref.watch(productViewModel).products,
                    loadingBuilder: (context) {
                      return ListView.separated(
                        padding: EdgeInsets.only(top: 16.h),
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemBuilder: (ctx, i) {
                          return Skeletonizer(
                            enabled: true,
                            child: ProductItem(
                              productData: ProductData(
                                name: 'product namw',
                                sellingPrice: '50000',
                                quantity: 25,
                                status: 'Active',
                                product: Product(
                                  category: Category(name: 'Shoe'),
                                  location: Location(name: 'Store 1'),
                                ),
                              ),
                            ),
                          );
                        },
                        separatorBuilder: (context, index) {
                          return SizedBox(height: 16.h);
                        },
                        itemCount: 4,
                      );
                    },
                    errorBuilder: (p0) {
                      return SizedBox(
                        height: 300.h,
                        child: ErrorState(
                          message: ref
                              .watch(productViewModel)
                              .fetchAllProductsMessage,
                          onPressed: () =>
                              ref.read(productViewModel).fetchAllProducts(),
                        ),
                      );
                    },
                    emptyBuilder: (context) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 16.0),
                        child: Padding(
                          padding: const EdgeInsets.only(top: 24.0),
                          child: EmptyState(
                              imageAsset: Utilities.getSvg("noData"),
                              showCTA: false,
                              title: "No Order",
                              subTitle:
                                  "You currently have not recent Order yet"),
                        ),
                      );
                    },
                    contentBuilder: (context) {
                      return ListView.separated(
                        padding: EdgeInsets.symmetric(vertical: 32.h),
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemBuilder: (ctx, i) {
                          final product = prodVm.products[i];
                          return ProductItem(
                            productData: product,
                            onPressed: () {
                              if (product.variationAttributes?.isNotEmpty ==
                                  true) {
                                // todo::: route to variation product
                                pushNavigation(
                                  context: context,
                                  widget: ViewProductVariationInfo(
                                    productId: product.product?.productId,
                                    variationId: product.variationId,
                                  ),
                                  routeName:
                                      NamedRoutes.viewProductVariationInfo,
                                );
                              } else {
                                // todo::: route to simple product
                                // We're using variation Id to get product data
                                pushNavigation(
                                    context: context,
                                    widget: ProductBasicInformation(
                                      viewProduct: true,
                                      variationId: product.variationId,
                                    ),
                                    routeName: NamedRoutes.productBasicInfo);
                              }
                            },
                          );
                        },
                        separatorBuilder: (context, index) {
                          return SizedBox(height: 22.h);
                        },
                        itemCount: prodVm.products.length,
                      );
                    },
                  )
                ],
              ),
      ),
    );
  }
}

// Widgets

class ProductManagementOverviewCard extends StatelessWidget {
  const ProductManagementOverviewCard({
    super.key,
    required this.revenue,
    required this.totalActive,
    required this.totalInactive,
    required this.totalProduct,
  });

  final double revenue;
  final int totalActive;
  final int totalInactive;
  final int totalProduct;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          color: ColorPath.flamingo.withOpacity(.08),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: ColorPath.flamingoRed)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "TOTAL REVENUE VALUE",
            style: textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600, color: colorScheme.text7),
          ),
          SizedBox(height: 8.h),
          NairaDisplay(
            amount: revenue,
            color: colorScheme.text6,
          ),
          // SizedBox(
          //   height: 8.h,
          // ),
          // Row(
          //   children: [
          //     ColorTag(
          //       color: ColorPath.emeraldGreen,
          //       child: Row(
          //         children: [
          //           Text(
          //             '0.5',
          //             style: textTheme.bodySmall?.copyWith(color: Colors.white),
          //           ),
          //           SizedBox(
          //             width: 2.w,
          //           ),
          //           const Icon(
          //             Icons.north_east,
          //             size: 12,
          //             color: Colors.white,
          //           )
          //         ],
          //       ),
          //     ),
          //     SizedBox(
          //       width: 8.w,
          //     ),
          //     Text(
          //       'last 3 days',
          //       style: textTheme.bodySmall,
          //     )
          //   ],
          // ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: ScreenTitle(
                  title: 'No. of Product',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.mirageBlack,
                  subTitle: Utilities.formatAmount(
                    addDecimal: false,
                    amount: totalProduct.toDouble(),
                  ),
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: ScreenTitle(
                  title: 'Active',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.salemGreen,
                  subTitle: Utilities.formatAmount(
                    addDecimal: false,
                    amount: totalActive.toDouble(),
                  ),
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: ScreenTitle(
                  title: 'Inactive',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.alizarinRed,
                  subTitle: Utilities.formatAmount(
                    addDecimal: false,
                    amount: totalInactive.toDouble(),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
