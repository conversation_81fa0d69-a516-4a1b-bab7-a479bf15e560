// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/create_product_arg.dart';
import 'package:quick_retail_mobile/core/data/models/product_response.dart';
import 'package:quick_retail_mobile/core/data/view_models/product_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/custom_bottom_sheet.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/take_product_action_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/busy_overlay.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/show_flush_bar.dart';

class ProductPricingAndInventoryInformation extends ConsumerStatefulWidget {
  const ProductPricingAndInventoryInformation({
    super.key,
    this.existingProductData,
    required this.createProductArg,
  });

  final ProductData? existingProductData;
  final CreateProductArg createProductArg;

  @override
  ConsumerState<ProductPricingAndInventoryInformation> createState() =>
      _ProductPricingAndInventoryInformationState();
}

class _ProductPricingAndInventoryInformationState
    extends ConsumerState<ProductPricingAndInventoryInformation> {
  final formKey = GlobalKey<FormState>();

  final costPriceController = TextEditingController();
  final sellingPriceController = TextEditingController();
  final availableQuantityController = TextEditingController();
  final reorderLevelController = TextEditingController();

  final costPriceFocusNode = FocusNode();
  final sellingPriceFocusNode = FocusNode();
  final availableQuantityFocusNode = FocusNode();
  final reorderLevelFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setViewProduct();
    });
  }

  setViewProduct() async {
    if (widget.existingProductData != null) {
      costPriceController.text =
          widget.existingProductData!.product?.costPrice?.toString() ?? '';
      sellingPriceController.text =
          widget.existingProductData!.product?.sellingPrice?.toString() ?? '';
      availableQuantityController.text =
          widget.existingProductData?.quantity?.toString() ?? '';
      reorderLevelController.text =
          widget.existingProductData!.reorderLevel?.toString() ?? '';

      setState(() {});
    }
  }

  @override
  void dispose() {
    costPriceController.dispose();
    sellingPriceController.dispose();
    availableQuantityController.dispose();
    reorderLevelController.dispose();
    costPriceFocusNode.dispose();
    sellingPriceFocusNode.dispose();
    availableQuantityFocusNode.dispose();
    reorderLevelFocusNode.dispose();

    super.dispose();
  }

  bool get formStateIsValid {
    return costPriceController.text.isNotEmpty &&
        sellingPriceController.text.isNotEmpty &&
        availableQuantityController.text.isNotEmpty &&
        reorderLevelController.text.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    // log(widget.createProductArg.productName ?? "");
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return BusyOverlay(
      show: ref.watch(productViewModel).createState == ViewState.busy,
      child: Scaffold(
        appBar: customAppBar(
          context: context,
          // preferredHeight: 16,
          title:
              "${widget.existingProductData != null ? "View" : "Add"} a Simple Product",
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 32.h),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: 1 + 1 == 3
                      ? 'PRICING AND INVENTORY INFORMATION'
                      : 'PRICING INFORMATION',
                  fontWeight: FontWeight.w500,
                  fontColor: colorScheme.text4,
                ),
                SizedBox(
                  height: 24.h,
                ),
                CustomTextField(
                  controller: costPriceController,
                  focusPointer: costPriceFocusNode,
                  label: 'Cost Price',
                  hintText: '',
                  prefixIcon: Padding(
                    padding: EdgeInsets.only(left: 12.w),
                    child: Text(
                      Utilities.naira,
                      style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.text7,
                          fontFamily: Platform.isAndroid ? '' : 'DMSans'),
                    ),
                  ),
                  onChanged: (v) => setState(() {}),
                ),
                // if (1 + 1 != 3)
                //   Column(
                //     children: [
                //       SizedBox(
                //         height: 16.h,
                //       ),
                //       CustomCheckBox(
                //         text: 'Apply to Variations',
                //         value: false,
                //       ),
                //     ],
                //   ),
                SizedBox(height: 24.h),
                CustomTextField(
                  controller: sellingPriceController,
                  focusPointer: sellingPriceFocusNode,
                  label: 'Selling Price',
                  hintText: '',
                  prefixIcon: Padding(
                    padding: EdgeInsets.only(left: 12.w),
                    child: Text(
                      Utilities.naira,
                      style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.text7,
                          fontFamily: Platform.isAndroid ? '' : 'DMSans'),
                    ),
                  ),
                  onChanged: (v) => setState(() {}),
                ),
                // if (1 + 1 != 3)
                //   Column(
                //     children: [
                //       SizedBox(
                //         height: 16.h,
                //       ),
                //       CustomCheckBox(
                //         text: 'Apply to Variations',
                //         value: false,
                //       ),
                //     ],
                //   ),
                // if (1 + 1 == 3)
                Column(
                  children: [
                    SizedBox(height: 24.h),
                    CustomTextField(
                      controller: availableQuantityController,
                      focusPointer: availableQuantityFocusNode,
                      label: 'Available Quantity',
                      hintText: 'Enter Value',
                      onChanged: (v) => setState(() {}),
                    ),
                    SizedBox(height: 24.h),
                    CustomTextField(
                      controller: reorderLevelController,
                      focusPointer: reorderLevelFocusNode,
                      label: 'Reorder Level',
                      hintText: 'Enter Value',
                      onChanged: (v) => setState(() {}),
                    ),
                    SizedBox(
                      height: 24.h,
                    ),
                  ],
                ),
                // SizedBox(
                //   height: 24.h,
                // ),
                // Clickable(
                //   onPressed: () {},
                //   child: const CustomTextField(
                //     label: 'Tax',
                //     hintText: 'Select an Option',
                //     enabled: false,
                //     suffixIcon: Padding(
                //       padding: EdgeInsets.only(right: 16.0),
                //       child: Icon(Icons.keyboard_arrow_down_outlined),
                //     ),
                //   ),
                // ),
                // SizedBox(height: 24.h),
                // Clickable(
                //   onPressed: () {},
                //   child: const CustomTextField(
                //     label: 'Discount',
                //     hintText: 'Select an Option',
                //     enabled: false,
                //     suffixIcon: Padding(
                //       padding: EdgeInsets.only(right: 16.0),
                //       child: Icon(Icons.keyboard_arrow_down_outlined),
                //     ),
                //   ),
                // ),
                // SizedBox(
                //   height: 24.h,
                // ),
                // const PromotionDetailsWidget(),
                SizedBox(height: 68.h),
                if (widget.existingProductData != null)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Clickable(
                        onPressed: () {
                          popNavigation(context: context);
                        },
                        child: Row(
                          children: [
                            SvgPicture.asset(Utilities.getSvg("arrowLeft")),
                            SizedBox(width: 8.w),
                            Text(
                              "Previous",
                              style: textTheme.bodyMedium?.copyWith(
                                  color: ColorPath.flamingo,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600),
                            ),
                          ],
                        ),
                      ),
                    ],
                  )
                else
                  CustomButton(
                    onPressed: () async {
                      FocusScope.of(context).unfocus();
                      if (!formKey.currentState!.validate()) return;

                      bottomSheetWrapper(
                          context: context,
                          child: CustomBottomSheet(
                            title: 'Create a Simple Product ?',
                            subTitle:
                                'Are you sure you want to Create a new Simple product inclusive of it related',
                            firstButtonText: "Yes, Create Product",
                            secondButtonText: "No, Close",
                            onPressedFirst: () async {
                              popNavigation(context: context);
                              await ref
                                  .read(productViewModel.notifier)
                                  .createProduct({
                                ...widget.createProductArg.toJson(),
                                "has_variations": false,
                                "cost_price":
                                    int.tryParse(costPriceController.text),
                                "selling_price":
                                    int.tryParse(sellingPriceController.text),
                                "total_quantity": int.tryParse(
                                    availableQuantityController.text),
                                "reorder_level":
                                    int.tryParse(reorderLevelController.text),
                              });

                              if (ref.read(productViewModel).createState ==
                                  ViewState.retrieved) {
                                bottomSheetWrapper(
                                    context: context,
                                    child: CustomBottomSheet(
                                      title: 'New Product Created',
                                      subTitle:
                                          'Congratulations, you have successfully created a new product',
                                      firstButtonText: "Manage all Product",
                                      secondButtonText: "Create New Product",
                                      onPressedFirst: () {
                                        popNavigation(context: context);
                                        popNavigation(context: context);
                                        popNavigation(context: context);
                                      },
                                      onPressedSecond: () {
                                        popNavigation(context: context);
                                      },
                                    ));
                              } else {
                                showFlushBar(
                                    context: context,
                                    message: ref.read(productViewModel).message,
                                    success: false);
                              }
                            },
                          ));

                      // pushNavigation(
                      //     context: context,
                      //     widget: const ProductVariationInformation(),
                      //     routeName: NamedRoutes.productVariation);
                    },
                    bgColor: !formStateIsValid
                        ? ColorPath.inactiveFlamingo
                        : ColorPath.flamingo,
                    buttonText: "Create a Simple Product",
                  )
              ],
            ),
          ),
        ),
        bottomSheet: widget.existingProductData != null
            ? Container(
                height: 163.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
                decoration: BoxDecoration(color: Colors.white, boxShadow: [
                  BoxShadow(
                      color: ColorPath.mischkaGrey,
                      blurRadius: 4.r,
                      spreadRadius: 4.r,
                      blurStyle: BlurStyle.outer)
                ]),
                child: Column(
                  children: [
                    Text(
                      "Edit, Delete or Deactivate a Simple Product",
                      style: textTheme.bodySmall
                          ?.copyWith(color: colorScheme.text7),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    CustomButton(
                      onPressed: () {
                        popNavigation(context: context);
                        bottomSheetWrapper(
                          context: context,
                          child: TakeProductActionBottomsheet(
                            viewProductArg: CreateProductArg(
                              variationId:
                                  widget.existingProductData?.variationId,
                              productId: widget
                                  .existingProductData?.product?.productId,
                            ),
                            // variationId:
                            //     widget.existingProductData?.variationId ?? "",
                            // productId: widget
                            //         .existingProductData?.product?.productId ??
                            //     "",
                          ),
                        );
                      },
                      borderColor: ColorPath.flamingo,
                      bgColor: Colors.white,
                      buttonTextColor: ColorPath.flamingo,
                      buttonText: "Take Action",
                    )
                  ],
                ),
              )
            : const SizedBox(),
      ),
    );
  }
}

// WIDGETS
class CustomCheckBox extends StatefulWidget {
  bool value;
  String text;
  CustomCheckBox({
    super.key,
    this.value = false,
    this.text = "",
  });

  @override
  State<CustomCheckBox> createState() => _CustomCheckBoxState();
}

class _CustomCheckBoxState extends State<CustomCheckBox> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Clickable(
            onPressed: () {
              setState(() {
                widget.value = !widget.value;
              });
            },
            child: Container(
              height: 24,
              width: 24,
              decoration: BoxDecoration(
                  color: widget.value ? ColorPath.flamingo : Colors.white,
                  borderRadius: BorderRadius.circular(6.r),
                  border: Border.all(
                      color: widget.value
                          ? Colors.transparent
                          : ColorPath.mischkaGrey)),
              child: Icon(
                Icons.done,
                size: 16.w,
                color: Colors.white,
              ),
            )),
        SizedBox(
          width: 12.w,
        ),
        Text(widget.text)
      ],
    );
  }
}

class PromotionDetailsWidget extends StatefulWidget {
  const PromotionDetailsWidget({
    super.key,
  });

  @override
  State<PromotionDetailsWidget> createState() => _PromotionDetailsWidgetState();
}

class _PromotionDetailsWidgetState extends State<PromotionDetailsWidget> {
  bool showFirst = true;
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          border: Border.all(color: ColorPath.mischkaGrey),
          borderRadius: BorderRadius.circular(8.r)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Clickable(
            onPressed: () {
              setState(() {
                showFirst = !showFirst;
              });
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                        child: Text(
                      'Promotion Details',
                      style: textTheme.bodyMedium
                          ?.copyWith(fontWeight: FontWeight.w600),
                    )),
                    Icon(
                      showFirst
                          ? Icons.keyboard_arrow_down_outlined
                          : Icons.keyboard_arrow_up_outlined,
                      color: colorScheme.subTextPrimary,
                    )
                  ],
                ),
                Text(
                  'Set specific production details',
                  style: textTheme.bodySmall
                      ?.copyWith(color: colorScheme.subTextPrimary),
                ),
              ],
            ),
          ),
          Column(
            children: [
              SizedBox(
                height: 16.h,
              ),
              AnimatedCrossFade(
                  sizeCurve: Curves.easeIn,
                  firstChild: const SizedBox(),
                  secondChild: Column(
                    children: [
                      CustomTextField(
                        label: 'Promotion Price',
                        hintText: '',
                        prefixIcon: Padding(
                          padding: EdgeInsets.only(left: 12.w),
                          child: Text(
                            Utilities.naira,
                            style: textTheme.bodyMedium?.copyWith(
                                color: colorScheme.text7,
                                fontFamily: Platform.isAndroid ? '' : 'DMSans'),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      Clickable(
                        onPressed: () {},
                        child: const CustomTextField(
                          label: 'Price Effective (Start Date)',
                          hintText: 'Select start date',
                          enabled: false,
                          suffixIcon: Padding(
                            padding: EdgeInsets.only(right: 16.0),
                            child: Icon(Icons.calendar_month_outlined),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      Clickable(
                        onPressed: () {},
                        child: const CustomTextField(
                          label: 'Price Effective (End Date)',
                          hintText: 'Select end date',
                          enabled: false,
                          suffixIcon: Padding(
                            padding: EdgeInsets.only(right: 16.0),
                            child: Icon(Icons.calendar_month_outlined),
                          ),
                        ),
                      ),
                    ],
                  ),
                  crossFadeState: showFirst
                      ? CrossFadeState.showFirst
                      : CrossFadeState.showSecond,
                  duration: const Duration(milliseconds: 50)),
            ],
          )
        ],
      ),
    );
  }
}
