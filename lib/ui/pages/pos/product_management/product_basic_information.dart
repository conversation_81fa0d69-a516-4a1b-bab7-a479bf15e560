import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/category_response.dart';
import 'package:quick_retail_mobile/core/data/models/create_product_arg.dart';
import 'package:quick_retail_mobile/core/data/models/location_target_response.dart';
import 'package:quick_retail_mobile/core/data/models/product_response.dart';
import 'package:quick_retail_mobile/core/data/view_models/category_view_model.dart';
import 'package:quick_retail_mobile/core/data/view_models/location_view_model.dart';
import 'package:quick_retail_mobile/core/data/view_models/product_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/image_and_doc_utils.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/core/utilities/validator.dart';
import 'package:quick_retail_mobile/ui/pages/pos/product_management/product_pricing_and_inventory_information.dart';
import 'package:quick_retail_mobile/ui/pages/pos/product_management/product_variation_information.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/product_category_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/store_location_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/take_product_action_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/busy_overlay.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/error_state.dart';
import 'package:quick_retail_mobile/ui/widgets/loadable_content_builder.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProductBasicInformation extends ConsumerStatefulWidget {
  const ProductBasicInformation({
    super.key,
    this.isVariation = false,
    this.viewProduct = false,
    this.variationId,
  });

  final bool isVariation;
  final bool viewProduct; // to view product
  final String? variationId; // We're using variation Id to get product data

  @override
  ConsumerState<ProductBasicInformation> createState() =>
      _ProductBasicInformationState();
}

class _ProductBasicInformationState
    extends ConsumerState<ProductBasicInformation> {
  final formKey = GlobalKey<FormState>();

  final productNameController = TextEditingController();
  final shortDescriptionController = TextEditingController();
  final longDescriptionController = TextEditingController();
  final skuController = TextEditingController();
  final categoryController = TextEditingController();
  final subCategoryController = TextEditingController();
  final tagsController = TextEditingController();
  final locationController = TextEditingController();

  final productNameFocusNode = FocusNode();
  final shortDescriptionFocusNode = FocusNode();
  final longDescriptionFocusNode = FocusNode();
  final skuFocusNode = FocusNode();
  final categoryFocusNode = FocusNode();
  final subCategoryFocusNode = FocusNode();
  final tagsFocusNode = FocusNode();
  final locationFocusNode = FocusNode();

  bool uploadingState = false;
  Category? selectedCategory;
  Category? selectedSubCategory;
  Store? selectedLocation;
  List<FileArg> imageFiles = [];

  ProductData? productData; // to view product
  List<String> imagePaths = []; // to view product

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(categoryViewModel).fetchAllCategories();
      ref.read(locationViewModel).fetchAllStoreLocations();
      setViewProduct();
    });
  }

  setViewProduct() async {
    if (widget.viewProduct == true) {
      ProductData? product = await ref
          .read(productViewModel)
          .fetchSingleProduct(widget.variationId ?? "");
      if (product != null) {
        productData = product;
        productNameController.text = product.name ?? '';
        shortDescriptionController.text =
            product.product?.shortDescription ?? '';
        longDescriptionController.text = product.product?.longDescription ?? '';
        skuController.text = product.sku ?? '';
        categoryController.text = product.product?.category?.name ?? '';
        subCategoryController.text = product.product?.subCategory?.name ?? '';
        tagsController.text = product.product?.tags ?? '';
        locationController.text = product.product?.location?.name ?? '';
        imagePaths =
            _convertImageStringToList(product.product?.imagePath ?? '');

        // set Categories
        selectedCategory = product.product?.category;
        selectedSubCategory = product.product?.subCategory;

        // get sub categories
        _getSubCategories(selectedCategory?.id ?? 0);

        setState(() {});
      }
    }
  }

  _getSubCategories(int categoryId) async {
    await ref.read(categoryViewModel).fetchAllSubCatByCatgoryId(
          categoryId: categoryId,
        );
  }

  List<String> _convertImageStringToList(String imageUrls) {
    // Split the string by commas and trim any whitespace
    List<String> imageList =
        imageUrls.split(',').map((url) => url.trim()).toList();
    return imageList;
  }

  @override
  void dispose() {
    productNameController.dispose();
    shortDescriptionController.dispose();
    longDescriptionController.dispose();
    skuController.dispose();
    categoryController.dispose();
    subCategoryController.dispose();
    tagsController.dispose();
    locationController.dispose();

    productNameFocusNode.dispose();
    // barcodeFocusNode.dispose();
    shortDescriptionFocusNode.dispose();
    longDescriptionFocusNode.dispose();
    skuFocusNode.dispose();
    categoryFocusNode.dispose();
    subCategoryFocusNode.dispose();
    tagsFocusNode.dispose();
    locationFocusNode.dispose();
    super.dispose();
  }

  bool get formStateIsValid {
    return productNameController.text.isNotEmpty &&
        selectedCategory != null &&
        selectedSubCategory != null &&
        skuController.text.isNotEmpty &&
        tagsController.text.isNotEmpty &&
        longDescriptionController.text.isNotEmpty &&
        shortDescriptionController.text.isNotEmpty &&
        imageFiles.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final productVm = ref.watch(productViewModel);
    return BusyOverlay(
      show: productVm.fetchSingleState == ViewState.busy,
      child: Scaffold(
        appBar: customAppBar(
          context: context,
          // preferredHeight: 16,
          title:
              " ${widget.viewProduct ? "View" : "Add a"} ${widget.isVariation ? "Variation" : "Simple"} Product",
          centerTitle: true,
        ),
        body: productVm.fetchSingleState == ViewState.error
            ? Center(
                child: ErrorState(
                  message: productVm.fetchSingleProductMessage,
                  onPressed: () => ref
                      .read(productViewModel)
                      .fetchSingleProduct(widget.variationId ?? ""),
                ),
              )
            : SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 32.h),
                child: Form(
                  key: formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        text: 'BASIC INFORMATION',
                        fontWeight: FontWeight.w500,
                        fontColor: colorScheme.text4,
                      ),
                      SizedBox(height: 24.h),
                      CustomTextField(
                        label: 'Product Name',
                        hintText: 'Enter product name',
                        validator: FieldValidator.validate,
                        controller: productNameController,
                        focusPointer: productNameFocusNode,
                        onChanged: (v) => setState(() {}),
                      ),
                      SizedBox(height: 24.h),
                      // CustomTextField(
                      //   label: 'Barcode',
                      //   hintText: 'Enter barcode',
                      //   controller: barcodeController,
                      //   focusPointer: barcodeFocusNode,
                      //   // validator: FieldValidator.validate,
                      //   // onChanged: (v) => setState(() {}),
                      //   suffixIcon: Clickable(
                      //     onPressed: () {
                      //       //todo::: route scan here
                      //     },
                      //     child: Padding(
                      //       padding: const EdgeInsets.only(left: 8.0, right: 12.0),
                      //       child: SvgPicture.asset(Utilities.getSvg('scan')),
                      //     ),
                      //   ),
                      // ),
                      // SizedBox(height: 24.h),
                      CustomTextField(
                        label: 'Short Description',
                        hintText: 'Enter short product description',
                        controller: shortDescriptionController,
                        validator: FieldValidator.validate,
                        maxLines: 3,
                        focusPointer: shortDescriptionFocusNode,
                        onChanged: (v) => setState(() {}),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      CustomTextField(
                        label: 'Long Description',
                        hintText: 'Enter other product details or instructions',
                        maxLines: 3,
                        isCompulsory: false,
                        controller: longDescriptionController,
                        validator: FieldValidator.validate,
                        focusPointer: longDescriptionFocusNode,
                        onChanged: (v) => setState(() {}),
                      ),
                      SizedBox(height: 24.h),
                      Row(
                        children: [
                          Text(
                            "Product Images",
                            style: textTheme.bodyMedium?.copyWith(
                                color: colorScheme.subTextSecondary,
                                fontSize: 14,
                                fontWeight: FontWeight.w500),
                          ),
                          SizedBox(
                            width: 5.w,
                          ),
                          Text(
                            '*',
                            style: textTheme.bodyMedium?.copyWith(
                                color: ColorPath.flamingoRed,
                                fontSize: 14,
                                fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                      SizedBox(height: 10.h),
                      LoadableContentBuilder(
                        isBusy: uploadingState,
                        items: imageFiles,
                        loadingBuilder: (context) {
                          return const Skeletonizer(
                            enabled: true,
                            child: UploadDocumentWidget(),
                          );
                        },
                        emptyBuilder: (context) {
                          return UploadDocumentWidget(
                            onPressed: () async {
                              uploadingState = true;
                              setState(() {});
                              List<File> files =
                                  await ImageAndDocUtils.pickMultipleImage(
                                      multiImage: true);

                              // Crop images and convert to base64
                              for (File file in files) {
                                File? croppedFile =
                                    await ImageAndDocUtils.cropImage(
                                        image: file);
                                if (croppedFile != null) {
                                  String base64String = await ImageAndDocUtils
                                      .fileToBase64ImageString(
                                          file: croppedFile);
                                  imageFiles.add(FileArg(
                                      file: croppedFile,
                                      base64String: base64String));
                                }
                              }
                              uploadingState = false;
                              setState(() {});
                            },
                          );
                        },
                        contentBuilder: (context) {
                          if (widget.viewProduct == true) {
                            return ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              padding: EdgeInsets.zero,
                              itemBuilder: (ctx, i) {
                                return UploadDocumentWidget(
                                  uploadedImage: imagePaths[i],
                                  uploadedImageName: "Image ${i + 1}",
                                  onPressed: () {
                                    imagePaths.removeAt(i);
                                    setState(() {});
                                  },
                                );
                              },
                              separatorBuilder: (_, __) =>
                                  SizedBox(height: 16.h),
                              itemCount: imagePaths.length,
                            );
                          }

                          return ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            padding: EdgeInsets.zero,
                            itemBuilder: (ctx, i) {
                              return UploadDocumentWidget(
                                uploadedImage: imageFiles[i].file,
                                uploadedImageName:
                                    imageFiles[i].file.path.split('/').last,
                                onPressed: () {
                                  imageFiles.removeAt(i);
                                  setState(() {});
                                },
                              );
                            },
                            separatorBuilder: (_, __) => SizedBox(height: 16.h),
                            itemCount: imageFiles.length,
                          );
                        },
                      ),
                      SizedBox(height: 32.h),
                      Clickable(
                        onPressed: () async {
                          // Add more photos
                          uploadingState = true;
                          setState(() {});
                          List<File> files =
                              await ImageAndDocUtils.pickMultipleImage(
                                  multiImage: true);

                          // Crop images and convert to base64
                          for (File file in files) {
                            File? croppedFile =
                                await ImageAndDocUtils.cropImage(image: file);
                            if (croppedFile != null) {
                              String base64String = await ImageAndDocUtils
                                  .fileToBase64ImageString(file: croppedFile);
                              imageFiles.add(FileArg(
                                  file: croppedFile,
                                  base64String: base64String));
                            }
                          }
                          uploadingState = false;
                          setState(() {});
                        },
                        child: Row(
                          children: [
                            Icon(
                              Icons.add,
                              color: ColorPath.flamingo,
                              size: 16.sp,
                            ),
                            SizedBox(
                              width: 6.w,
                            ),
                            Text(
                              "Add More Photos",
                              style: textTheme.bodyMedium?.copyWith(
                                  color: ColorPath.flamingo,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      CustomTextField(
                        label: 'SKU (Store Keeping Unit) ',
                        hintText: 'Enter SKU',
                        isCompulsory: false,
                        controller: skuController,
                        validator: FieldValidator.validate,
                        focusPointer: skuFocusNode,
                        onChanged: (v) => setState(() {}),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      Clickable(
                        onPressed: () {
                          bottomSheetWrapper(
                            context: context,
                            child: ProductCategoryBottomsheet(
                              returningValue: (value) {
                                log("returningValue ${value.id}");
                                selectedCategory = value;
                                categoryController.text = value.name ?? '';
                                ref
                                    .read(categoryViewModel)
                                    .fetchAllSubCatByCatgoryId(
                                        categoryId: value.id ?? 0);
                              },
                            ),
                          );
                        },
                        child: CustomTextField(
                          label: 'Category',
                          hintText: 'Select product category ',
                          controller: categoryController,
                          validator: FieldValidator.validate,
                          focusPointer: categoryFocusNode,
                          onChanged: (v) => setState(() {}),
                          enabled: false,
                          suffixIcon: const Padding(
                            padding: EdgeInsets.only(right: 16.0),
                            child: Icon(Icons.keyboard_arrow_down_outlined),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      Clickable(
                        onPressed: () {
                          bottomSheetWrapper(
                            context: context,
                            child: ProductCategoryBottomsheet(
                              isSubCat: true,
                              returningValue: (value) {
                                log("returningValue ${value.id}");
                                selectedSubCategory = value;
                                subCategoryController.text = value.name ?? '';
                              },
                            ),
                          );
                        },
                        child: CustomTextField(
                          label: 'Sub-category',
                          hintText: ' Select sub-category ',
                          enabled: false,
                          controller: subCategoryController,
                          validator: FieldValidator.validate,
                          focusPointer: subCategoryFocusNode,
                          onChanged: (v) => setState(() {}),
                          suffixIcon: const Padding(
                            padding: EdgeInsets.only(right: 16.0),
                            child: Icon(Icons.keyboard_arrow_down_outlined),
                          ),
                        ),
                      ),
                      SizedBox(height: 24.h),

                      CustomTextField(
                        label: 'Tags',
                        hintText: 'Enter tags',
                        // isCompulsory: false,
                        controller: tagsController,
                        validator: FieldValidator.validate,
                        focusPointer: tagsFocusNode,
                        onChanged: (v) => setState(() {}),
                      ),

                      // todo::: handle tag section here
                      // SizedBox(height: 24.h),
                      // const CustomTextField(
                      //   label: 'Supplier',
                      //   hintText: 'Enter supplier',
                      //   isCompulsory: false,
                      // ),
                      SizedBox(height: 24.h),
                      Clickable(
                        onPressed: () {
                          bottomSheetWrapper(
                            context: context,
                            child: StoreLocationBottomsheet(
                              returningValue: (value) {
                                log("returningValue ${value.id}");
                                selectedLocation = value;
                                locationController.text = value.name ?? '';
                                setState(() {});
                              },
                            ),
                          );
                        },
                        child: CustomTextField(
                          label: 'Location',
                          hintText: 'Select Location ',
                          controller: locationController,
                          focusPointer: locationFocusNode,
                          validator: FieldValidator.validate,
                          onChanged: (v) => setState(() {}),
                          enabled: false,
                          suffixIcon: const Padding(
                            padding: EdgeInsets.only(right: 16.0),
                            child: Icon(Icons.keyboard_arrow_down_outlined),
                          ),
                        ),
                      ),
                      SizedBox(height: 48.h),

                      // if User is not viewing product
                      if (!widget.viewProduct)
                        CustomButton(
                          onPressed: () {
                            FocusScope.of(context).unfocus();
                            if (!formKey.currentState!.validate()) return;

                            final createProdParams = CreateProductArg(
                              productName: productNameController.text,
                              sku: skuController.text,
                              // ean: barcodeController.text,
                              categoryId: selectedCategory?.id,
                              subCategoryId: selectedSubCategory?.id,
                              tags: tagsController.text,
                              shortDescription: shortDescriptionController.text,
                              longDescription: longDescriptionController.text,
                              imagePath: imageFiles
                                  .map((e) => e.base64String)
                                  .toList(),
                              imageFile: imageFiles.map((e) => e.file).toList(),
                              locationId: selectedLocation?.id,
                            );

                            pushNavigation(
                              context: context,
                              widget: widget.isVariation
                                  ? ProductVariationInformation(
                                      createProductArg: createProdParams,
                                    )
                                  : ProductPricingAndInventoryInformation(
                                      createProductArg: createProdParams,
                                    ),
                              routeName: widget.isVariation
                                  ? NamedRoutes.productVariation
                                  : NamedRoutes.productPricingAndInventory,
                            );
                          },
                          bgColor: !formStateIsValid
                              ? ColorPath.inactiveFlamingo
                              : ColorPath.flamingo,
                          buttonText: "Continue",
                        )
                      else
                        Clickable(
                          onPressed: () {
                            final createProdParams = CreateProductArg(
                              productName: productNameController.text,
                              sku: skuController.text,
                              // ean: barcodeController.text,
                              categoryId: selectedCategory?.id,
                              subCategoryId: selectedSubCategory?.id,
                              tags: tagsController.text,
                              shortDescription: shortDescriptionController.text,
                              longDescription: longDescriptionController.text,
                              imagePath: imageFiles
                                  .map((e) => e.base64String)
                                  .toList(),
                              imageFile: imageFiles.map((e) => e.file).toList(),
                              locationId: selectedLocation?.id,
                            );

                            pushNavigation(
                              context: context,
                              widget: ProductPricingAndInventoryInformation(
                                existingProductData: productData,
                                createProductArg: createProdParams,
                              ),
                              routeName: NamedRoutes.productPricingAndInventory,
                            );
                          },
                          child: Row(
                            children: [
                              Text(
                                "Next",
                                style: textTheme.bodyMedium?.copyWith(
                                    color: ColorPath.flamingo,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600),
                              ),
                              SizedBox(width: 8.w),
                              SvgPicture.asset(Utilities.getSvg("arrowRight"))
                            ],
                          ),
                        ),
                      if (widget.viewProduct) // if User is viewing product
                        SizedBox(height: 163.h)
                    ],
                  ),
                ),
              ),
        bottomSheet: widget.viewProduct &&
                productVm.fetchSingleState == ViewState.retrieved
            ? Container(
                height: 163.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
                decoration: BoxDecoration(color: Colors.white, boxShadow: [
                  BoxShadow(
                      color: ColorPath.mischkaGrey,
                      blurRadius: 4.r,
                      spreadRadius: 4.r,
                      blurStyle: BlurStyle.outer)
                ]),
                child: Column(
                  children: [
                    Text(
                      "Edit, Delete or Deactivate a ${widget.isVariation ? "Variation" : "Simple"} Product",
                      style: textTheme.bodySmall
                          ?.copyWith(color: colorScheme.text7),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    CustomButton(
                      onPressed: () {
                        /// todo::: handle edit here
                        /// Add only fields user edited

                        final viewProductParams = CreateProductArg(
                          productId: productData?.product?.productId,
                          variationId: productData?.variationId,
                          productName: productNameController.text,
                          sku: skuController.text,
                          // ean: barcodeController.text,
                          categoryId: selectedCategory?.id,
                          subCategoryId: selectedSubCategory?.id,
                          tags: tagsController.text,
                          shortDescription: shortDescriptionController.text,
                          longDescription: longDescriptionController.text,
                          // imagePath: imageFiles
                          //     .map((e) => e.base64String)
                          //     .toList(),
                          hasVariations: widget.isVariation,
                          imageFile: imageFiles.map((e) => e.file).toList(),
                          locationId: selectedLocation?.id,
                        );

                        bottomSheetWrapper(
                          context: context,
                          child: TakeProductActionBottomsheet(
                            viewProductArg: viewProductParams,
                            // variationId: widget.variationId ?? "",
                            // productId: productData?.product?.productId ?? "",
                          ),
                        );
                      },
                      borderColor: ColorPath.flamingo,
                      bgColor: Colors.white,
                      buttonTextColor: ColorPath.flamingo,
                      buttonText: "Take Action",
                    )
                  ],
                ),
              )
            : const SizedBox(),
      ),
    );
  }
}

// todo::: handle multiple uloads here
class UploadDocumentWidget extends StatelessWidget {
  const UploadDocumentWidget({
    super.key,
    this.uploadedImage,
    this.uploadedImageName,
    this.onPressed,
  });

  final dynamic uploadedImage;
  final String? uploadedImageName;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(14.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: ColorPath.mischkaGrey)),
          child: Row(
            children: [
              if (uploadedImage != null)
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: (uploadedImage is File)
                      ? Image.file(
                          uploadedImage!,
                          height: 48.h,
                          width: 48.w,
                          fit: BoxFit.cover,
                        )
                      : Image.network(
                          uploadedImage!,
                          height: 48.h,
                          width: 48.w,
                          fit: BoxFit.cover,
                        ),
                )
              else
                Skeleton.replace(
                    replacement: Bone.circle(
                      size: 40.w,
                    ),
                    child: SvgPicture.asset(
                      Utilities.getSvg('document'),
                    )),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: Text(
                  uploadedImageName ?? 'No upload / Attachment',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: textTheme.bodyMedium
                      ?.copyWith(color: colorScheme.subTextPrimary),
                ),
              ),
              SizedBox(width: 8.w),
              Clickable(
                onPressed: onPressed,
                child: Container(
                  padding: EdgeInsets.all(10.w),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      color: ColorPath.flamingo),
                  child: Text(
                    uploadedImage != null ? "Remove" : "Upload",
                    style: textTheme.bodySmall?.copyWith(
                        color: Colors.white, fontWeight: FontWeight.w500),
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }
}
